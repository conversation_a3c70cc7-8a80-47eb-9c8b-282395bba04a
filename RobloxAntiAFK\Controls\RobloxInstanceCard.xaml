<UserControl x:Class="RobloxAntiAFK.Controls.RobloxInstanceCard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="120" d:DesignWidth="400">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/LiquidGlassStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- Status to Color Converter -->
            <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
                <Setter Property="Width" Value="12"/>
                <Setter Property="Height" Value="12"/>
                <Setter Property="Fill" Value="{StaticResource AccentRedBrush}"/>
                <Style.Triggers>
                    <DataTrigger Binding="{Binding IsRunning}" Value="True">
                        <Setter Property="Fill" Value="{StaticResource AccentGreenBrush}"/>
                    </DataTrigger>
                    <DataTrigger Binding="{Binding IsProtected}" Value="True">
                        <Setter Property="Fill" Value="{StaticResource AccentBlueBrush}"/>
                    </DataTrigger>
                </Style.Triggers>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>

    <Border Style="{StaticResource GlassCardStyle}" x:Name="MainCard">
        <Border.RenderTransform>
            <ScaleTransform ScaleX="1" ScaleY="1"/>
        </Border.RenderTransform>
        <Border.RenderTransformOrigin>
            <Point X="0.5" Y="0.5"/>
        </Border.RenderTransformOrigin>
        
        <Border.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.4">
                            <DoubleAnimation.EasingFunction>
                                <CubicEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)" 
                                       From="0.9" To="1" Duration="0:0:0.4">
                            <DoubleAnimation.EasingFunction>
                                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)" 
                                       From="0.9" To="1" Duration="0:0:0.4">
                            <DoubleAnimation.EasingFunction>
                                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Border.Triggers>

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Instance Information -->
            <StackPanel Grid.Column="0" VerticalAlignment="Center">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Status Indicator with Dynamic Glow -->
                    <Grid Grid.Column="0" VerticalAlignment="Center" Margin="0,0,8,0">
                        <!-- Default (Red) Status Indicator -->
                        <Ellipse>
                            <Ellipse.Effect>
                                <DropShadowEffect Color="Red" BlurRadius="8" ShadowDepth="0" Opacity="0.6"/>
                            </Ellipse.Effect>
                            <Ellipse.Style>
                                <Style TargetType="Ellipse" BasedOn="{StaticResource StatusIndicatorStyle}">
                                    <Setter Property="Visibility" Value="Visible"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsRunning}" Value="True">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding IsProtected}" Value="True">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Ellipse.Style>
                        </Ellipse>

                        <!-- Running (Green) Status Indicator -->
                        <Ellipse>
                            <Ellipse.Effect>
                                <DropShadowEffect Color="LimeGreen" BlurRadius="8" ShadowDepth="0" Opacity="0.6"/>
                            </Ellipse.Effect>
                            <Ellipse.Style>
                                <Style TargetType="Ellipse" BasedOn="{StaticResource StatusIndicatorStyle}">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding IsRunning}" Value="True"/>
                                                <Condition Binding="{Binding IsProtected}" Value="False"/>
                                            </MultiDataTrigger.Conditions>
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </MultiDataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Ellipse.Style>
                        </Ellipse>

                        <!-- Protected (Blue) Status Indicator -->
                        <Ellipse>
                            <Ellipse.Effect>
                                <DropShadowEffect Color="DodgerBlue" BlurRadius="8" ShadowDepth="0" Opacity="0.6"/>
                            </Ellipse.Effect>
                            <Ellipse.Style>
                                <Style TargetType="Ellipse" BasedOn="{StaticResource StatusIndicatorStyle}">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsProtected}" Value="True">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Ellipse.Style>
                        </Ellipse>
                    </Grid>

                    <!-- Instance Name -->
                    <TextBlock Grid.Column="1" Text="{Binding DisplayName}" 
                               Style="{StaticResource GlassSubheaderStyle}"
                               TextTrimming="CharacterEllipsis" Margin="0"/>
                </Grid>

                <!-- Process Info -->
                <TextBlock Style="{StaticResource GlassTextStyle}" Opacity="0.7" FontSize="12" Margin="20,2,0,0">
                    <Run Text="PID: "/>
                    <Run Text="{Binding ProcessId}"/>
                    <Run Text=" • Started: "/>
                    <Run Text="{Binding StartTime, StringFormat=HH:mm:ss}"/>
                </TextBlock>

                <!-- Status Text -->
                <TextBlock Text="{Binding StatusText}" FontSize="13" FontWeight="Medium" Margin="20,4,0,0">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource GlassTextStyle}">
                            <Setter Property="Foreground" Value="{StaticResource AccentRedBrush}"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsRunning}" Value="True">
                                    <Setter Property="Foreground" Value="{StaticResource AccentGreenBrush}"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding IsProtected}" Value="True">
                                    <Setter Property="Foreground" Value="{StaticResource AccentBlueBrush}"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>

                <!-- Last Activity (only show if protected) -->
                <TextBlock Style="{StaticResource GlassTextStyle}" Opacity="0.6" FontSize="11" 
                           Margin="20,2,0,0" Visibility="{Binding IsProtected, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Run Text="Last Activity: "/>
                    <Run Text="{Binding LastActivity, StringFormat=HH:mm:ss}"/>
                </TextBlock>
            </StackPanel>

            <!-- Toggle Button -->
            <Button Grid.Column="1" x:Name="ToggleButton"
                    VerticalAlignment="Center" MinWidth="100"
                    Command="{Binding DataContext.ToggleProtectionCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                    CommandParameter="{Binding}">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="{Binding IsProtected, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='🛡️ Protected|🔓 Protect'}"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Button.Content>
                <Button.Style>
                    <Style TargetType="Button" BasedOn="{StaticResource GlassButtonStyle}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsProtected}" Value="True">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#40007ACC" Offset="0"/>
                                            <GradientStop Color="#20007ACC" Offset="0.5"/>
                                            <GradientStop Color="#10007ACC" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="BorderBrush" Value="{StaticResource AccentBlueBrush}"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding IsRunning}" Value="False">
                                <Setter Property="IsEnabled" Value="False"/>
                                <Setter Property="Opacity" Value="0.5"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </Grid>
    </Border>
</UserControl>
