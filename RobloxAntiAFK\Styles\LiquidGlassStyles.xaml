<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Modern Liquid Glass Color Palette -->
    <Color x:Key="AccentBlue">#0078D4</Color>
    <Color x:Key="AccentGreen">#00CC6A</Color>
    <Color x:Key="AccentRed">#FF4444</Color>
    <Color x:Key="AccentOrange">#FF8C00</Color>
    <Color x:Key="TextPrimary">#FFFFFF</Color>
    <Color x:Key="TextSecondary">#B3FFFFFF</Color>

    <!-- Brushes -->
    <SolidColorBrush x:Key="AccentBlueBrush" Color="{StaticResource AccentBlue}"/>
    <SolidColorBrush x:Key="AccentGreenBrush" Color="{StaticResource AccentGreen}"/>
    <SolidColorBrush x:Key="AccentRedBrush" Color="{StaticResource AccentRed}"/>
    <SolidColorBrush x:Key="AccentOrangeBrush" Color="{StaticResource AccentOrange}"/>
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimary}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondary}"/>

    <!-- Ultra Modern Glass Effect Brushes - Mimicking Reference Image -->
    <RadialGradientBrush x:Key="GlassCardBrush" Center="0.5,0.2" RadiusX="1.5" RadiusY="1.5">
        <GradientStop Color="#70FFFFFF" Offset="0"/>
        <GradientStop Color="#40FFFFFF" Offset="0.3"/>
        <GradientStop Color="#20FFFFFF" Offset="0.7"/>
        <GradientStop Color="#10FFFFFF" Offset="1"/>
    </RadialGradientBrush>

    <RadialGradientBrush x:Key="GlassButtonBrush" Center="0.5,0.1" RadiusX="1.2" RadiusY="1.2">
        <GradientStop Color="#90FFFFFF" Offset="0"/>
        <GradientStop Color="#50FFFFFF" Offset="0.2"/>
        <GradientStop Color="#30FFFFFF" Offset="0.6"/>
        <GradientStop Color="#15FFFFFF" Offset="1"/>
    </RadialGradientBrush>

    <LinearGradientBrush x:Key="GlassBorderBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#A0FFFFFF" Offset="0"/>
        <GradientStop Color="#60FFFFFF" Offset="0.3"/>
        <GradientStop Color="#30FFFFFF" Offset="0.7"/>
        <GradientStop Color="#15FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <!-- Enhanced Glass Effects for Different Elements -->
    <RadialGradientBrush x:Key="GlassInstanceCardBrush" Center="0.5,0.15" RadiusX="1.3" RadiusY="1.3">
        <GradientStop Color="#80FFFFFF" Offset="0"/>
        <GradientStop Color="#45FFFFFF" Offset="0.25"/>
        <GradientStop Color="#25FFFFFF" Offset="0.65"/>
        <GradientStop Color="#12FFFFFF" Offset="1"/>
    </RadialGradientBrush>

    <!-- Smooth Animations -->
    <Storyboard x:Key="FadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- Modern Glass Button Style -->
    <Style x:Key="GlassButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource GlassButtonBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource GlassBorderBrush}"/>
        <Setter Property="BorderThickness" Value="0.5"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Padding" Value="20,10"/>
        <Setter Property="Margin" Value="6"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="20">
                        <Border.Effect>
                            <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="4"
                                            BlurRadius="20" Opacity="0.4"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                       To="1.02" Duration="0:0:0.15"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                       To="1.02" Duration="0:0:0.15"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                                       To="1" Duration="0:0:0.15"/>
                                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                                       To="1" Duration="0:0:0.15"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="RenderTransform">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Glass Card Style -->
    <Style x:Key="GlassCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource GlassCardBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource GlassBorderBrush}"/>
        <Setter Property="BorderThickness" Value="0.5"/>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Margin" Value="12"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="6"
                                BlurRadius="25" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Text Styles -->
    <Style x:Key="GlassTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
    </Style>

    <Style x:Key="GlassHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource GlassTextStyle}">
        <Setter Property="FontSize" Value="22"/>
        <Setter Property="FontWeight" Value="Light"/>
        <Setter Property="Margin" Value="0,0,0,12"/>
    </Style>

    <Style x:Key="GlassSubheaderStyle" TargetType="TextBlock" BasedOn="{StaticResource GlassTextStyle}">
        <Setter Property="FontSize" Value="15"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Margin" Value="0,0,0,6"/>
    </Style>

    <Style x:Key="GlassSecondaryTextStyle" TargetType="TextBlock" BasedOn="{StaticResource GlassTextStyle}">
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="FontSize" Value="12"/>
    </Style>

    <!-- Modern Toggle Switch Style -->
    <Style x:Key="GlassToggleStyle" TargetType="CheckBox">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <StackPanel Orientation="Horizontal">
                        <Border x:Name="ToggleTrack" Width="44" Height="24"
                                Background="{StaticResource GlassCardBrush}"
                                BorderBrush="{StaticResource GlassBorderBrush}"
                                BorderThickness="0.5" CornerRadius="12" Margin="0,0,12,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="2"
                                                BlurRadius="8" Opacity="0.3"/>
                            </Border.Effect>
                            <Ellipse x:Name="ToggleThumb" Width="18" Height="18"
                                     Fill="{StaticResource TextSecondaryBrush}"
                                     HorizontalAlignment="Left" Margin="3,0,0,0">
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#60000000" Direction="270" ShadowDepth="1"
                                                    BlurRadius="4" Opacity="0.5"/>
                                </Ellipse.Effect>
                            </Ellipse>
                        </Border>
                        <ContentPresenter VerticalAlignment="Center"/>
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ToggleThumb" Property="HorizontalAlignment" Value="Right"/>
                            <Setter TargetName="ToggleThumb" Property="Margin" Value="0,0,3,0"/>
                            <Setter TargetName="ToggleThumb" Property="Fill" Value="{StaticResource AccentBlueBrush}"/>
                            <Setter TargetName="ToggleTrack" Property="BorderBrush" Value="{StaticResource AccentBlueBrush}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ToggleTrack" Property="BorderBrush" Value="{StaticResource AccentBlueBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Glass Slider Style -->
    <Style x:Key="GlassSliderStyle" TargetType="Slider">
        <Setter Property="Height" Value="24"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Slider">
                    <Grid>
                        <Border x:Name="TrackBackground" Height="6"
                                Background="{StaticResource GlassCardBrush}"
                                BorderBrush="{StaticResource GlassBorderBrush}"
                                BorderThickness="0.5" CornerRadius="3"
                                VerticalAlignment="Center">
                            <Border.Effect>
                                <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="2"
                                                BlurRadius="6" Opacity="0.3"/>
                            </Border.Effect>
                        </Border>
                        <Track x:Name="PART_Track">
                            <Track.DecreaseRepeatButton>
                                <RepeatButton Style="{x:Null}" Background="Transparent" Focusable="False"/>
                            </Track.DecreaseRepeatButton>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton Style="{x:Null}" Background="Transparent" Focusable="False"/>
                            </Track.IncreaseRepeatButton>
                            <Track.Thumb>
                                <Thumb Width="20" Height="20">
                                    <Thumb.Template>
                                        <ControlTemplate TargetType="Thumb">
                                            <Border Background="{StaticResource AccentBlueBrush}"
                                                    BorderBrush="{StaticResource TextPrimaryBrush}"
                                                    BorderThickness="1.5"
                                                    CornerRadius="10">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#60000000" Direction="270"
                                                                    ShadowDepth="3" BlurRadius="10" Opacity="0.4"/>
                                                </Border.Effect>
                                            </Border>
                                        </ControlTemplate>
                                    </Thumb.Template>
                                </Thumb>
                            </Track.Thumb>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Status Indicator Style -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="12"/>
        <Setter Property="Height" Value="12"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#80000000" Direction="270" ShadowDepth="1"
                                BlurRadius="4" Opacity="0.6"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Instance Card Style - Enhanced Glass Effect -->
    <Style x:Key="InstanceCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource GlassInstanceCardBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource GlassBorderBrush}"/>
        <Setter Property="BorderThickness" Value="0.8"/>
        <Setter Property="CornerRadius" Value="18"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Margin" Value="0,10"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#60000000" Direction="270" ShadowDepth="6"
                                BlurRadius="25" Opacity="0.4"/>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
