<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Modern Liquid Glass Color Palette -->
    <Color x:Key="AccentBlue">#0078D4</Color>
    <Color x:Key="AccentGreen">#00CC6A</Color>
    <Color x:Key="AccentRed">#FF4444</Color>
    <Color x:Key="AccentOrange">#FF8C00</Color>
    <Color x:Key="TextPrimary">#FFFFFF</Color>
    <Color x:Key="TextSecondary">#B3FFFFFF</Color>

    <!-- Brushes -->
    <SolidColorBrush x:Key="AccentBlueBrush" Color="{StaticResource AccentBlue}"/>
    <SolidColorBrush x:Key="AccentGreenBrush" Color="{StaticResource AccentGreen}"/>
    <SolidColorBrush x:Key="AccentRedBrush" Color="{StaticResource AccentRed}"/>
    <SolidColorBrush x:Key="AccentOrangeBrush" Color="{StaticResource AccentOrange}"/>
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimary}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondary}"/>

    <!-- True Apple-Style Glass Effects -->
    <LinearGradientBrush x:Key="AppleGlassBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#B0FFFFFF" Offset="0"/>
        <GradientStop Color="#80FFFFFF" Offset="0.1"/>
        <GradientStop Color="#40FFFFFF" Offset="0.5"/>
        <GradientStop Color="#20FFFFFF" Offset="0.9"/>
        <GradientStop Color="#10FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="AppleButtonBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#D0FFFFFF" Offset="0"/>
        <GradientStop Color="#A0FFFFFF" Offset="0.05"/>
        <GradientStop Color="#60FFFFFF" Offset="0.3"/>
        <GradientStop Color="#30FFFFFF" Offset="0.8"/>
        <GradientStop Color="#15FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="AppleBorderBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#E0FFFFFF" Offset="0"/>
        <GradientStop Color="#80FFFFFF" Offset="0.5"/>
        <GradientStop Color="#40FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="AppleCardBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#C0FFFFFF" Offset="0"/>
        <GradientStop Color="#90FFFFFF" Offset="0.08"/>
        <GradientStop Color="#50FFFFFF" Offset="0.4"/>
        <GradientStop Color="#25FFFFFF" Offset="0.85"/>
        <GradientStop Color="#12FFFFFF" Offset="1"/>
    </LinearGradientBrush>

    <!-- Smooth Animations -->
    <Storyboard x:Key="FadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- Apple-Style Glass Button -->
    <Style x:Key="GlassButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource AppleButtonBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource AppleBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="SF Pro Display, Segoe UI"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="24,12"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="22">
                        <Border.Effect>
                            <DropShadowEffect Color="#60000000" Direction="270" ShadowDepth="8"
                                            BlurRadius="30" Opacity="0.3"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#F0FFFFFF" Offset="0"/>
                                        <GradientStop Color="#C0FFFFFF" Offset="0.05"/>
                                        <GradientStop Color="#80FFFFFF" Offset="0.3"/>
                                        <GradientStop Color="#50FFFFFF" Offset="0.8"/>
                                        <GradientStop Color="#25FFFFFF" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#A0FFFFFF" Offset="0"/>
                                        <GradientStop Color="#70FFFFFF" Offset="0.05"/>
                                        <GradientStop Color="#40FFFFFF" Offset="0.3"/>
                                        <GradientStop Color="#20FFFFFF" Offset="0.8"/>
                                        <GradientStop Color="#10FFFFFF" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Apple-Style Glass Card -->
    <Style x:Key="GlassCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource AppleGlassBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource AppleBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1.5"/>
        <Setter Property="CornerRadius" Value="24"/>
        <Setter Property="Padding" Value="32"/>
        <Setter Property="Margin" Value="16"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#80000000" Direction="270" ShadowDepth="12"
                                BlurRadius="40" Opacity="0.4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Text Styles -->
    <Style x:Key="GlassTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
    </Style>

    <Style x:Key="GlassHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource GlassTextStyle}">
        <Setter Property="FontSize" Value="22"/>
        <Setter Property="FontWeight" Value="Light"/>
        <Setter Property="Margin" Value="0,0,0,12"/>
    </Style>

    <Style x:Key="GlassSubheaderStyle" TargetType="TextBlock" BasedOn="{StaticResource GlassTextStyle}">
        <Setter Property="FontSize" Value="15"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Margin" Value="0,0,0,6"/>
    </Style>

    <Style x:Key="GlassSecondaryTextStyle" TargetType="TextBlock" BasedOn="{StaticResource GlassTextStyle}">
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="FontSize" Value="12"/>
    </Style>

    <!-- Apple-Style Toggle Switch -->
    <Style x:Key="GlassToggleStyle" TargetType="CheckBox">
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="SF Pro Display, Segoe UI"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="0,12"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <StackPanel Orientation="Horizontal">
                        <Border x:Name="ToggleTrack" Width="52" Height="28"
                                Background="{StaticResource AppleCardBrush}"
                                BorderBrush="{StaticResource AppleBorderBrush}"
                                BorderThickness="1" CornerRadius="14" Margin="0,0,16,0">
                            <Border.Effect>
                                <DropShadowEffect Color="#60000000" Direction="270" ShadowDepth="4"
                                                BlurRadius="12" Opacity="0.4"/>
                            </Border.Effect>
                            <Ellipse x:Name="ToggleThumb" Width="22" Height="22"
                                     Fill="{StaticResource TextPrimaryBrush}"
                                     HorizontalAlignment="Left" Margin="3,0,0,0">
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#80000000" Direction="270" ShadowDepth="3"
                                                    BlurRadius="8" Opacity="0.6"/>
                                </Ellipse.Effect>
                            </Ellipse>
                        </Border>
                        <ContentPresenter VerticalAlignment="Center"/>
                    </StackPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ToggleThumb" Property="HorizontalAlignment" Value="Right"/>
                            <Setter TargetName="ToggleThumb" Property="Margin" Value="0,0,3,0"/>
                            <Setter TargetName="ToggleThumb" Property="Fill" Value="{StaticResource AccentBlueBrush}"/>
                            <Setter TargetName="ToggleTrack" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#E0007ACC" Offset="0"/>
                                        <GradientStop Color="#B0007ACC" Offset="0.1"/>
                                        <GradientStop Color="#80007ACC" Offset="0.5"/>
                                        <GradientStop Color="#60007ACC" Offset="0.9"/>
                                        <GradientStop Color="#40007ACC" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ToggleTrack" Property="BorderBrush" Value="{StaticResource AccentBlueBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Apple-Style Modern Slider -->
    <Style x:Key="GlassSliderStyle" TargetType="Slider">
        <Setter Property="Height" Value="32"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Slider">
                    <Grid>
                        <Border x:Name="TrackBackground" Height="8"
                                Background="{StaticResource AppleCardBrush}"
                                BorderBrush="{StaticResource AppleBorderBrush}"
                                BorderThickness="1" CornerRadius="4"
                                VerticalAlignment="Center">
                            <Border.Effect>
                                <DropShadowEffect Color="#60000000" Direction="270" ShadowDepth="4"
                                                BlurRadius="12" Opacity="0.3"/>
                            </Border.Effect>
                        </Border>
                        <Track x:Name="PART_Track">
                            <Track.DecreaseRepeatButton>
                                <RepeatButton Style="{x:Null}" Background="Transparent" Focusable="False"/>
                            </Track.DecreaseRepeatButton>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton Style="{x:Null}" Background="Transparent" Focusable="False"/>
                            </Track.IncreaseRepeatButton>
                            <Track.Thumb>
                                <Thumb Width="24" Height="24">
                                    <Thumb.Template>
                                        <ControlTemplate TargetType="Thumb">
                                            <Border Background="{StaticResource AccentBlueBrush}"
                                                    BorderBrush="{StaticResource TextPrimaryBrush}"
                                                    BorderThickness="2"
                                                    CornerRadius="12">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#80000000" Direction="270"
                                                                    ShadowDepth="6" BlurRadius="16" Opacity="0.5"/>
                                                </Border.Effect>
                                            </Border>
                                        </ControlTemplate>
                                    </Thumb.Template>
                                </Thumb>
                            </Track.Thumb>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Status Indicator Style -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="12"/>
        <Setter Property="Height" Value="12"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#80000000" Direction="270" ShadowDepth="1"
                                BlurRadius="4" Opacity="0.6"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Apple-Style Instance Card -->
    <Style x:Key="InstanceCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource AppleCardBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource AppleBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1.2"/>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="Padding" Value="28"/>
        <Setter Property="Margin" Value="0,12"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#80000000" Direction="270" ShadowDepth="8"
                                BlurRadius="32" Opacity="0.4"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Custom ScrollViewer Style -->
    <Style x:Key="AppleScrollViewer" TargetType="ScrollViewer">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollViewer">
                    <Grid>
                        <ScrollContentPresenter x:Name="PART_ScrollContentPresenter"/>
                        <ScrollBar x:Name="PART_VerticalScrollBar"
                                   Orientation="Vertical"
                                   HorizontalAlignment="Right"
                                   Width="8" Margin="0,4"
                                   Style="{StaticResource AppleScrollBarStyle}"/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Apple-Style ScrollBar -->
    <Style x:Key="AppleScrollBarStyle" TargetType="ScrollBar">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollBar">
                    <Border Background="{TemplateBinding Background}" CornerRadius="4">
                        <Track x:Name="PART_Track" IsDirectionReversed="True">
                            <Track.Thumb>
                                <Thumb>
                                    <Thumb.Template>
                                        <ControlTemplate TargetType="Thumb">
                                            <Border Background="{StaticResource AppleCardBrush}"
                                                    CornerRadius="4" Margin="1">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#60000000" Direction="270"
                                                                    ShadowDepth="2" BlurRadius="6" Opacity="0.3"/>
                                                </Border.Effect>
                                            </Border>
                                        </ControlTemplate>
                                    </Thumb.Template>
                                </Thumb>
                            </Track.Thumb>
                        </Track>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
