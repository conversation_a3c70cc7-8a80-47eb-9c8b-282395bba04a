using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace RobloxAntiAFK.Converters
{
    public class BooleanToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isRunning)
            {
                return isRunning ? Color.FromRgb(0x00, 0xCC, 0x6A) : Color.FromRgb(0xFF, 0x44, 0x44);
            }
            return Color.FromRgb(0xFF, 0x44, 0x44);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
