<Window x:Class="RobloxAntiAFK.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:RobloxAntiAFK"
        xmlns:converters="clr-namespace:RobloxAntiAFK.Converters"
        xmlns:tb="http://www.hardcodet.net/taskbar"
        mc:Ignorable="d"
        Title="Roblox Anti-AFK" Height="600" Width="800" MinHeight="400" MinWidth="600"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent"
        WindowStartupLocation="CenterScreen"
        UseLayoutRounding="True" SnapsToDevicePixels="True"
        RenderOptions.BitmapScalingMode="HighQuality"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Styles/LiquidGlassStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
            <converters:BooleanToStringConverter x:Key="BooleanToStringConverter"/>
            <converters:BooleanToColorConverter x:Key="BooleanToColorConverter"/>
            <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <!-- Subtle Background -->
        <Rectangle Fill="#15000000" RadiusX="20" RadiusY="20" Margin="20"/>

        <!-- System Tray Icon -->
        <tb:TaskbarIcon x:Name="TrayIcon"
                        ToolTipText="Roblox Anti-AFK"
                        Visibility="Visible">
            <tb:TaskbarIcon.ContextMenu>
                <ContextMenu>
                    <MenuItem Header="Show" Click="ShowWindow_Click"/>
                    <MenuItem Header="Hide" Click="HideWindow_Click"/>
                    <Separator/>
                    <MenuItem Header="Exit" Click="ExitApplication_Click"/>
                </ContextMenu>
            </tb:TaskbarIcon.ContextMenu>
        </tb:TaskbarIcon>

        <!-- Main Glass Container -->
        <Border Style="{StaticResource GlassCardStyle}" Margin="24">
            <Border.Effect>
                <DropShadowEffect Color="#80000000" Direction="270" ShadowDepth="15"
                                BlurRadius="50" Opacity="0.6"/>
            </Border.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Title Bar -->
                <Grid Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="🎮" FontSize="20" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="Roblox Anti-AFK" Style="{StaticResource GlassHeaderStyle}" Margin="0"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Content="−" Style="{StaticResource GlassButtonStyle}"
                                Width="36" Height="36" Padding="0" Margin="4,0"
                                Click="MinimizeButton_Click" FontSize="18"/>
                        <Button Content="✕" Style="{StaticResource GlassButtonStyle}"
                                Width="36" Height="36" Padding="0" Margin="4,0"
                                Click="CloseButton_Click" FontSize="14"/>
                    </StackPanel>
                </Grid>

                <!-- Status & Controls -->
                <Grid Grid.Row="1" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Status -->
                    <StackPanel Grid.Column="0" VerticalAlignment="Center">
                        <TextBlock Text="{Binding StatusMessage}" Style="{StaticResource GlassTextStyle}" FontSize="15" FontWeight="Medium"/>
                        <TextBlock Style="{StaticResource GlassSecondaryTextStyle}" Margin="0,2,0,0">
                            <Run Text="Found: "/>
                            <Run Text="{Binding TotalCount}"/>
                            <Run Text=" • Active: "/>
                            <Run Text="{Binding ProtectedCount}"/>
                        </TextBlock>
                    </StackPanel>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Content="Refresh" Style="{StaticResource GlassButtonStyle}"
                                Click="RefreshButton_Click"/>
                        <Button Content="Enable All" Style="{StaticResource GlassButtonStyle}"
                                Click="ProtectAllButton_Click"/>
                    </StackPanel>
                </Grid>

                <!-- Instances List -->
                <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                    <ItemsControl ItemsSource="{Binding RobloxInstances}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Style="{StaticResource InstanceCardStyle}">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Status Indicator -->
                                        <Ellipse Grid.Column="0" Style="{StaticResource StatusIndicatorStyle}" VerticalAlignment="Center" Margin="0,0,20,0">
                                            <Ellipse.Fill>
                                                <SolidColorBrush Color="{Binding IsRunning, Converter={StaticResource BooleanToColorConverter}}"/>
                                            </Ellipse.Fill>
                                        </Ellipse>

                                        <!-- Instance Info -->
                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding DisplayName}" Style="{StaticResource GlassTextStyle}" FontSize="16" FontWeight="Medium"/>
                                            <TextBlock Style="{StaticResource GlassSecondaryTextStyle}" Margin="0,4,0,0">
                                                <Run Text="PID: "/>
                                                <Run Text="{Binding ProcessId}"/>
                                            </TextBlock>
                                        </StackPanel>

                                        <!-- Toggle Button -->
                                        <Button Grid.Column="2" Style="{StaticResource GlassButtonStyle}"
                                                VerticalAlignment="Center"
                                                Command="{Binding DataContext.ToggleProtectionCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                CommandParameter="{Binding}">
                                            <Button.Content>
                                                <TextBlock Text="{Binding IsProtected, Converter={StaticResource BooleanToStringConverter}}"/>
                                            </Button.Content>
                                        </Button>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- Empty State (shown when no instances) -->
                <StackPanel Grid.Row="2" VerticalAlignment="Center" HorizontalAlignment="Center"
                            Visibility="{Binding TotalCount, Converter={StaticResource CountToVisibilityConverter}}">
                    <TextBlock Text="No Roblox instances found" Style="{StaticResource GlassTextStyle}"
                               FontSize="18" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock Text="Launch Roblox and click Refresh" Style="{StaticResource GlassSecondaryTextStyle}"
                               FontSize="14" HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Settings -->
                <Grid Grid.Row="3" Margin="0,20,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Anti-AFK Settings -->
                    <StackPanel Grid.Column="0" Margin="0,0,20,0">
                        <TextBlock Text="Settings" Style="{StaticResource GlassSubheaderStyle}" Margin="0,0,0,16"/>

                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Interval" Style="{StaticResource GlassTextStyle}"
                                       VerticalAlignment="Center" Margin="0,0,12,0"/>
                            <Slider Grid.Column="1" Value="{Binding AfkInterval}" Minimum="60" Maximum="600"
                                    TickFrequency="30" IsSnapToTickEnabled="True" Style="{StaticResource GlassSliderStyle}"/>
                            <TextBlock Grid.Column="2" Style="{StaticResource GlassTextStyle}"
                                       VerticalAlignment="Center" Margin="12,0,0,0">
                                <Run Text="{Binding AfkInterval}"/>
                                <Run Text="s"/>
                            </TextBlock>
                        </Grid>

                        <CheckBox Content="Random intervals" IsChecked="{Binding UseRandomInterval}"
                                  Style="{StaticResource GlassToggleStyle}"/>
                    </StackPanel>

                    <!-- App Settings -->
                    <StackPanel Grid.Column="1">
                        <TextBlock Text="Options" Style="{StaticResource GlassSubheaderStyle}" Margin="0,0,0,16"/>

                        <CheckBox Content="Auto-refresh" IsChecked="{Binding AutoRefresh}"
                                  Style="{StaticResource GlassToggleStyle}"/>

                        <CheckBox Content="Minimize to tray" IsChecked="{Binding MinimizeToTray}"
                                  Style="{StaticResource GlassToggleStyle}"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>
    </Grid>
</Window>
